<?php

namespace App\Models;

use PDO;

/**
 * Modelo para el historial de productos activos por mes/año
 */
class ProductHistorical extends Model
{
    protected $table = 'product_historical';
    
    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
    }
    
    /**
     * Registra el estado de los productos activos para un mes y año específicos
     * 
     * @param int $month Mes (1-12)
     * @param int $year Año (ej. 2023)
     * @param array $productIds IDs de los productos activos
     * @param int $userId ID del usuario que realiza el registro
     * @return bool Éxito de la operación
     */
    public function registerActiveProducts($month, $year, $productIds, $userId)
    {
        try {
            // Iniciar transacción
            $this->db->beginTransaction();
            
            // Primero, verificar si ya existe un registro para este mes/año
            $sql = "SELECT id FROM {$this->table} WHERE month = ? AND year = ? LIMIT 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$month, $year]);
            $existingRecord = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Si existe, eliminar los detalles anteriores
            if ($existingRecord) {
                $historicalId = $existingRecord['id'];
                $deleteSql = "DELETE FROM product_historical_details WHERE historical_id = ?";
                $deleteStmt = $this->db->prepare($deleteSql);
                $deleteStmt->execute([$historicalId]);
            } else {
                // Crear nuevo registro histórico
                $insertSql = "INSERT INTO {$this->table} (month, year, created_at, user_id) VALUES (?, ?, NOW(), ?)";
                $insertStmt = $this->db->prepare($insertSql);
                $insertStmt->execute([$month, $year, $userId]);
                $historicalId = $this->db->lastInsertId();
            }
            
            // Insertar los productos activos en la tabla de detalles
            if (!empty($productIds)) {
                $detailsSql = "INSERT INTO product_historical_details (historical_id, product_id) VALUES (?, ?)";
                $detailsStmt = $this->db->prepare($detailsSql);
                
                foreach ($productIds as $productId) {
                    $detailsStmt->execute([$historicalId, $productId]);
                }
            }
            
            // Confirmar transacción
            $this->db->commit();
            return true;
            
        } catch (\PDOException $e) {
            // Revertir en caso de error
            $this->db->rollBack();
            error_log("Error en ProductHistorical::registerActiveProducts: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Obtiene los productos activos para un mes y año específicos
     * 
     * @param int $month Mes (1-12)
     * @param int $year Año (ej. 2023)
     * @return array Arreglo con los productos activos
     */
    public function getActiveProducts($month, $year)
    {
        try {
            $sql = "SELECT p.* 
                    FROM products p
                    INNER JOIN product_historical_details phd ON p.id = phd.product_id
                    INNER JOIN {$this->table} ph ON phd.historical_id = ph.id
                    WHERE ph.month = ? AND ph.year = ?
                    ORDER BY p.name";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$month, $year]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (\PDOException $e) {
            error_log("Error en ProductHistorical::getActiveProducts: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Obtiene todos los registros históricos disponibles
     * 
     * @return array Arreglo con los registros históricos
     */
    public function getAllHistoricalRecords()
    {
        try {
            $sql = "SELECT ph.*, 
                    u.name as user_name,
                    COUNT(phd.product_id) as product_count
                    FROM {$this->table} ph
                    LEFT JOIN product_historical_details phd ON ph.id = phd.historical_id
                    LEFT JOIN users u ON ph.user_id = u.id
                    GROUP BY ph.id
                    ORDER BY ph.year DESC, ph.month DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (\PDOException $e) {
            error_log("Error en ProductHistorical::getAllHistoricalRecords: " . $e->getMessage());
            return [];
        }
    }
}