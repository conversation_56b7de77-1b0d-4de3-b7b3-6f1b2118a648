<?php
// Test simple para verificar productos
require_once __DIR__ . '/vendor/autoload.php';

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Test Simple de Productos</h1>";

try {
    // Conexión directa
    $db = \App\Core\Database::getInstance();
    $conn = $db->getConnection();
    
    echo "<h2>1. Conexión OK</h2>";
    
    // Verificar productos directamente
    $stmt = $conn->query("SELECT COUNT(*) as total FROM products");
    $result = $stmt->fetch();
    echo "<p>Total productos en DB: " . $result['total'] . "</p>";
    
    if ($result['total'] > 0) {
        // Mostrar algunos productos
        $stmt = $conn->query("SELECT id, sku, name, price FROM products LIMIT 5");
        $products = $stmt->fetchAll();
        
        echo "<h3>Productos encontrados:</h3>";
        echo "<ul>";
        foreach ($products as $product) {
            echo "<li>ID: {$product['id']} - SKU: {$product['sku']} - {$product['name']} - \${$product['price']}</li>";
        }
        echo "</ul>";
        
        // Probar el modelo
        echo "<h2>2. Probando modelo Product</h2>";
        $productModel = new \App\Models\Product();
        $modelProducts = $productModel->getAllWithDetails();
        echo "<p>Productos desde modelo: " . count($modelProducts) . "</p>";
        
        if (count($modelProducts) > 0) {
            echo "<h3>Primer producto del modelo:</h3>";
            echo "<pre>";
            print_r($modelProducts[0]);
            echo "</pre>";
        }
        
    } else {
        echo "<p style='color: red;'>No hay productos en la base de datos</p>";
        
        // Verificar si las tablas existen
        echo "<h3>Verificando tablas:</h3>";
        $tables = ['products', 'categories', 'product_types', 'product_status'];
        foreach ($tables as $table) {
            $stmt = $conn->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$table]);
            if ($stmt->rowCount() > 0) {
                $countStmt = $conn->query("SELECT COUNT(*) as count FROM $table");
                $count = $countStmt->fetch();
                echo "<p>✅ $table: {$count['count']} registros</p>";
            } else {
                echo "<p>❌ $table: NO EXISTE</p>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='/marykaystock/products'>Ir a la página de productos</a></p>";
?>
