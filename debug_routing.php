<?php
/**
 * Debug del sistema de routing y productos
 */

require_once __DIR__ . '/vendor/autoload.php';

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug del Sistema de Routing</h1>";

try {
    // 1. Verificar autoload
    echo "<h2>1. Verificando autoload...</h2>";
    if (class_exists('\App\Core\Database')) {
        echo "✅ Clase Database cargada<br>";
    } else {
        echo "❌ Clase Database NO cargada<br>";
    }
    
    if (class_exists('\App\Models\Product')) {
        echo "✅ Clase Product cargada<br>";
    } else {
        echo "❌ Clase Product NO cargada<br>";
    }
    
    if (class_exists('\App\Controllers\ProductController')) {
        echo "✅ Clase ProductController cargada<br>";
    } else {
        echo "❌ Clase ProductController NO cargada<br>";
    }
    echo "<br>";
    
    // 2. Verificar conexión DB
    echo "<h2>2. Verificando conexión a base de datos...</h2>";
    $db = \App\Core\Database::getInstance();
    $conn = $db->getConnection();
    echo "✅ Conexión exitosa<br><br>";
    
    // 3. Verificar productos en DB
    echo "<h2>3. Verificando productos en base de datos...</h2>";
    $stmt = $conn->query("SELECT COUNT(*) as total FROM products");
    $result = $stmt->fetch();
    echo "📊 Total productos: " . $result['total'] . "<br>";
    
    if ($result['total'] > 0) {
        $stmt = $conn->query("SELECT id, sku, name FROM products LIMIT 3");
        $products = $stmt->fetchAll();
        echo "<ul>";
        foreach ($products as $product) {
            echo "<li>ID: {$product['id']} - {$product['sku']} - {$product['name']}</li>";
        }
        echo "</ul>";
    }
    echo "<br>";
    
    // 4. Probar modelo Product
    echo "<h2>4. Probando modelo Product...</h2>";
    $productModel = new \App\Models\Product();
    $modelProducts = $productModel->getAllWithDetails();
    echo "📊 Productos desde modelo: " . count($modelProducts) . "<br>";
    
    if (count($modelProducts) > 0) {
        echo "<h3>Primer producto:</h3>";
        $firstProduct = $modelProducts[0];
        echo "<ul>";
        echo "<li>ID: " . ($firstProduct['id'] ?? 'N/A') . "</li>";
        echo "<li>SKU: " . ($firstProduct['sku'] ?? 'N/A') . "</li>";
        echo "<li>Nombre: " . ($firstProduct['name'] ?? 'N/A') . "</li>";
        echo "<li>Precio: $" . ($firstProduct['price'] ?? 'N/A') . "</li>";
        echo "<li>Categoría: " . ($firstProduct['category_name'] ?? 'N/A') . "</li>";
        echo "<li>Stock: " . ($firstProduct['current_stock'] ?? 'N/A') . "</li>";
        echo "</ul>";
    }
    echo "<br>";
    
    // 5. Simular controlador
    echo "<h2>5. Simulando controlador de productos...</h2>";
    session_start();
    
    try {
        $controller = new \App\Controllers\ProductController();
        
        // Capturar output
        ob_start();
        $controller->index();
        $output = ob_get_clean();
        
        if (strlen($output) > 0) {
            echo "✅ Controlador ejecutado correctamente<br>";
            echo "📊 Tamaño de salida: " . strlen($output) . " caracteres<br>";
            
            // Buscar indicadores en la salida
            if (strpos($output, 'No hay productos') !== false) {
                echo "⚠️ El controlador indica que no hay productos<br>";
            } elseif (strpos($output, 'products-tree') !== false) {
                echo "✅ Se encontró el contenedor de productos<br>";
            }
        } else {
            echo "❌ El controlador no generó salida<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Error en controlador: " . $e->getMessage() . "<br>";
    }
    echo "<br>";
    
    // 6. Verificar constantes
    echo "<h2>6. Verificando constantes...</h2>";
    if (defined('BASE_URL')) {
        echo "✅ BASE_URL definida: " . BASE_URL . "<br>";
    } else {
        echo "❌ BASE_URL no definida<br>";
    }
    echo "<br>";
    
    // 7. Información del servidor
    echo "<h2>7. Información del servidor...</h2>";
    echo "<ul>";
    echo "<li>REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'N/A') . "</li>";
    echo "<li>SCRIPT_NAME: " . ($_SERVER['SCRIPT_NAME'] ?? 'N/A') . "</li>";
    echo "<li>PHP_SELF: " . ($_SERVER['PHP_SELF'] ?? 'N/A') . "</li>";
    echo "<li>DOCUMENT_ROOT: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'N/A') . "</li>";
    echo "<li>Directorio actual: " . __DIR__ . "</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error general:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<h3>Enlaces de prueba:</h3>";
echo "<ul>";
echo "<li><a href='/marykaystock/'>Inicio</a></li>";
echo "<li><a href='/marykaystock/products'>Productos</a></li>";
echo "<li><a href='/marykaystock/login'>Login</a></li>";
echo "</ul>";
?>
