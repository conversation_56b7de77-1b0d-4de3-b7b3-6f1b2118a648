<?php
/**
 * Simular exactamente lo que hace la página de productos
 */

require_once __DIR__ . '/vendor/autoload.php';

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Simulación Exacta de la Página de Productos</h1>";

try {
    // 1. Definir BASE_URL como lo hace index.php
    $requestUri = $_SERVER['REQUEST_URI'];
    $scriptName = $_SERVER['SCRIPT_NAME'];
    $basePath = dirname($scriptName);
    if ($basePath === '/') {
        $basePath = '';
    }
    define('BASE_URL', $basePath);
    
    echo "<h2>1. Configuración inicial</h2>";
    echo "BASE_URL definida como: " . BASE_URL . "<br>";
    
    // 2. Iniciar sesión como lo hace index.php
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    echo "✅ Sesión iniciada<br><br>";
    
    // 3. Verificar conexión DB
    echo "<h2>2. Verificando base de datos</h2>";
    $db = \App\Core\Database::getInstance();
    $conn = $db->getConnection();
    
    $stmt = $conn->query("SELECT COUNT(*) as total FROM products");
    $result = $stmt->fetch();
    echo "📊 Productos en DB: " . $result['total'] . "<br>";
    
    if ($result['total'] == 0) {
        echo "<p style='color: red;'>❌ No hay productos. <a href='/marykaystock/init_database.php'>Ejecutar init_database.php</a></p>";
        exit;
    }
    echo "<br>";
    
    // 4. Ejecutar exactamente el código del controlador
    echo "<h2>3. Ejecutando lógica del ProductController::index()</h2>";
    
    try {
        // Crear subcategorías predeterminadas para todas las categorías que no tienen
        $subcategoryModel = new \App\Models\Subcategory();
        $subcategoryModel->createDefaultSubcategoriesForAllCategories();
        echo "✅ Subcategorías predeterminadas creadas<br>";
        
        // Obtener todos los productos con información de categoría y subcategoría
        $productModel = new \App\Models\Product();
        $products = $productModel->getAllWithDetails();
        echo "✅ Productos obtenidos: " . count($products) . "<br>";
        
        // Obtener todas las categorías y subcategorías para asegurar que se muestren incluso sin productos
        $categoryModel = new \App\Models\Category();
        $categories = $categoryModel->getAll();
        echo "✅ Categorías obtenidas: " . count($categories) . "<br>";
        
        $subcategories = $subcategoryModel->getAll();
        echo "✅ Subcategorías obtenidas: " . count($subcategories) . "<br>";
        
        // Datos que se pasarían a la vista
        $viewData = [
            'title' => 'Productos',
            'products' => $products,
            'categories' => $categories,
            'subcategories' => $subcategories
        ];
        
        echo "<h3>Datos para la vista:</h3>";
        echo "<ul>";
        echo "<li>title: " . $viewData['title'] . "</li>";
        echo "<li>products: " . count($viewData['products']) . " elementos</li>";
        echo "<li>categories: " . count($viewData['categories']) . " elementos</li>";
        echo "<li>subcategories: " . count($viewData['subcategories']) . " elementos</li>";
        echo "</ul>";
        
        // 5. Simular la renderización de la vista
        echo "<h2>4. Simulando renderización de vista</h2>";
        
        if (empty($products)) {
            echo "<div style='border: 2px solid red; padding: 10px; background: #ffe6e6;'>";
            echo "<h3>❌ La vista mostraría: 'No hay productos registrados.'</h3>";
            echo "</div>";
        } else {
            echo "<div style='border: 2px solid green; padding: 10px; background: #e6ffe6;'>";
            echo "<h3>✅ La vista debería mostrar los productos organizados</h3>";
            
            // Simular la organización de productos como en la vista
            $organizedProducts = [];
            foreach ($products as $product) {
                $categoryId = $product['category_id'] ?? 0;
                $subcategoryId = $product['subcategory_id'] ?? 0;
                
                if (!isset($organizedProducts[$categoryId])) {
                    $organizedProducts[$categoryId] = [
                        'name' => $product['category_name'] ?? '',
                        'description' => $product['category_description'] ?? '',
                        'subcategories' => []
                    ];
                }
                
                if (!isset($organizedProducts[$categoryId]['subcategories'][$subcategoryId])) {
                    $organizedProducts[$categoryId]['subcategories'][$subcategoryId] = [
                        'name' => $product['subcategory_name'] ?? '',
                        'description' => $product['subcategory_description'] ?? '',
                        'products' => []
                    ];
                }
                
                $organizedProducts[$categoryId]['subcategories'][$subcategoryId]['products'][] = $product;
            }
            
            echo "<h4>Estructura que se mostraría:</h4>";
            foreach ($organizedProducts as $catId => $category) {
                echo "<div style='margin-left: 20px; border-left: 2px solid #ccc; padding-left: 10px;'>";
                echo "<strong>📁 {$category['name']} (ID: $catId)</strong><br>";
                foreach ($category['subcategories'] as $subId => $subcategory) {
                    echo "<div style='margin-left: 20px;'>";
                    echo "<em>📂 {$subcategory['name']} (ID: $subId) - " . count($subcategory['products']) . " productos</em><br>";
                    foreach ($subcategory['products'] as $product) {
                        echo "<div style='margin-left: 20px; color: #666;'>";
                        echo "📦 {$product['name']} (SKU: {$product['sku']}) - \${$product['price']}<br>";
                        echo "</div>";
                    }
                    echo "</div>";
                }
                echo "</div><br>";
            }
            echo "</div>";
        }
        
        // 6. Verificar el archivo de vista
        echo "<h2>5. Verificando archivo de vista</h2>";
        $viewFile = __DIR__ . "/app/views/products/index.php";
        if (file_exists($viewFile)) {
            echo "✅ Archivo de vista existe: $viewFile<br>";
            echo "📊 Tamaño del archivo: " . filesize($viewFile) . " bytes<br>";
        } else {
            echo "❌ Archivo de vista NO existe: $viewFile<br>";
        }
        
        // 7. Verificar layout principal
        $layoutFile = __DIR__ . "/app/views/layouts/main.php";
        if (file_exists($layoutFile)) {
            echo "✅ Layout principal existe: $layoutFile<br>";
        } else {
            echo "❌ Layout principal NO existe: $layoutFile<br>";
        }
        
    } catch (\Exception $e) {
        echo "<div style='border: 2px solid red; padding: 10px; background: #ffe6e6;'>";
        echo "<h3>❌ Error en la lógica del controlador:</h3>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error crítico:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<h3>🔗 Enlaces de prueba:</h3>";
echo "<ul>";
echo "<li><a href='/marykaystock/products' target='_blank'>🔗 Abrir página de productos real</a></li>";
echo "<li><a href='/marykaystock/init_database.php'>🔧 Ejecutar init_database.php</a></li>";
echo "<li><a href='/marykaystock/debug_products_detailed.php'>🔍 Debug detallado</a></li>";
echo "</ul>";
?>
