<?php
/**
 * Punto de entrada principal del proyecto Mary Kay Stock
 * Este archivo redirige todas las solicitudes a la carpeta public
 */

// Obtener la URI solicitada
$requestUri = $_SERVER['REQUEST_URI'];
$scriptName = $_SERVER['SCRIPT_NAME'];

// Obtener la ruta base del proyecto
$basePath = dirname($scriptName);
if ($basePath === '/') {
    $basePath = '';
}

// Limpiar la URI de la ruta base
$cleanUri = str_replace($basePath, '', $requestUri);
$cleanUri = ltrim($cleanUri, '/');

// Si es una solicitud de archivo estático, verificar si existe en public
if (preg_match('/\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/i', $cleanUri)) {
    $staticFile = __DIR__ . '/public/' . $cleanUri;
    if (file_exists($staticFile)) {
        // Establecer el tipo de contenido correcto
        $extension = strtolower(pathinfo($staticFile, PATHINFO_EXTENSION));
        $contentTypes = [
            'js' => 'application/javascript',
            'css' => 'text/css',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'ico' => 'image/x-icon',
            'svg' => 'image/svg+xml',
            'woff' => 'font/woff',
            'woff2' => 'font/woff2',
            'ttf' => 'font/ttf',
            'eot' => 'application/vnd.ms-fontobject'
        ];
        
        if (isset($contentTypes[$extension])) {
            header('Content-Type: ' . $contentTypes[$extension]);
            
            // Agregar headers de cache para archivos estáticos
            $expires = 60 * 60 * 24 * 30; // 30 días
            header('Cache-Control: public, max-age=' . $expires);
            header('Expires: ' . gmdate('D, d M Y H:i:s', time() + $expires) . ' GMT');
        }
        
        readfile($staticFile);
        exit;
    }
}

// Incluir el autoloader de Composer
require_once __DIR__ . '/vendor/autoload.php';

// Configuración de errores para desarrollo
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Definir constantes actualizadas para la nueva estructura
define('BASE_URL', $basePath . '/marykaystock');

// Iniciar sesión
session_start();

// Obtener instancia del router
$router = \App\Core\Router::getInstance();

// Configurar rutas principales
$router->add('', ['controller' => 'Home', 'action' => 'index']);
$router->add('login', ['controller' => 'Auth', 'action' => 'login']);
$router->add('logout', ['controller' => 'Auth', 'action' => 'logout']);

// Rutas de productos
$router->add('products', ['controller' => 'Product', 'action' => 'index']);
$router->add('products/create', ['controller' => 'Product', 'action' => 'create']);
$router->add('products/edit/([0-9]+)', ['controller' => 'Product', 'action' => 'edit']);
$router->add('products/delete/([0-9]+)', ['controller' => 'Product', 'action' => 'delete']);
$router->add('products/bulk-price-update', ['controller' => 'Product', 'action' => 'updateBulkPrices']);
$router->add('products/bulk-status-update', ['controller' => 'Product', 'action' => 'bulkStatusUpdate']);
$router->add('products/bulk-status-update-process', ['controller' => 'Product', 'action' => 'bulkStatusUpdateProcess']);

// Rutas de stock
$router->add('stock', ['controller' => 'Stock', 'action' => 'index']);
$router->add('stock/movements', ['controller' => 'Stock', 'action' => 'movements']);
$router->add('stock/low', ['controller' => 'Stock', 'action' => 'lowStock']);

// Rutas de reportes
$router->add('reports', ['controller' => 'Report', 'action' => 'index']);
$router->add('reports/movements', ['controller' => 'Report', 'action' => 'movementsByPeriod']);
$router->add('reports/critical', ['controller' => 'Report', 'action' => 'criticalStock']);
$router->add('reports/inventory', ['controller' => 'Report', 'action' => 'inventory']);
$router->add('reports/adjustments', ['controller' => 'Report', 'action' => 'adjustments']);

// Rutas AJAX
$router->add('ajax/subcategories/([0-9]+)', [
    'controller' => 'Ajax',
    'action' => 'getSubcategories'
]);

// Rutas de órdenes de compra
$router->add('purchase-orders', ['controller' => 'PurchaseOrder', 'action' => 'index']);
$router->add('purchase-orders/create', ['controller' => 'PurchaseOrder', 'action' => 'create']);
$router->add('purchase-orders/view/([0-9]+)', ['controller' => 'PurchaseOrder', 'action' => 'view']);
$router->add('purchase-orders/period-report', ['controller' => 'PurchaseOrder', 'action' => 'periodReport']);

// Rutas de reportes de precios
$router->add('reports/price-history', ['controller' => 'Report', 'action' => 'priceHistory']);
$router->add('reports/monthly-prices', ['controller' => 'Report', 'action' => 'monthlyPrices']);

// Procesar la URL
$url = $cleanUri;

// Remover parámetros GET de la URL para el routing
$urlParts = parse_url('/' . $url);
$path = isset($urlParts['path']) ? trim($urlParts['path'], '/') : '';

// Mantener los parámetros GET intactos
try {
    $router->dispatch($path);
} catch (Exception $e) {
    error_log("Router Error: " . $e->getMessage() . " for URL: " . $path);
    
    // Cargar la vista de error 404
    if (file_exists(__DIR__ . '/app/views/errors/404.php')) {
        include __DIR__ . '/app/views/errors/404.php';
    } else {
        // Fallback si no existe la vista de error
        http_response_code(404);
        echo '<!DOCTYPE html>
<html>
<head>
    <title>404 - Página no encontrada</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
        h1 { color: #e74c3c; }
    </style>
</head>
<body>
    <h1>404 - Página no encontrada</h1>
    <p>La página que buscas no existe.</p>
    <a href="' . BASE_URL . '">Volver al inicio</a>
</body>
</html>';
    }
}
