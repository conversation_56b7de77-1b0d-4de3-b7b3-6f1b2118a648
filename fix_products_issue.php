<?php
/**
 * Script para diagnosticar y arreglar el problema de productos
 */

require_once __DIR__ . '/vendor/autoload.php';

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Diagnóstico y Reparación del Problema de Productos</h1>";

try {
    $db = \App\Core\Database::getInstance();
    $conn = $db->getConnection();
    
    // 1. Verificar todas las tablas necesarias
    echo "<h2>1. Verificando tablas necesarias</h2>";
    $requiredTables = [
        'products',
        'categories', 
        'subcategories',
        'product_types',
        'product_status',
        'stock_movements'
    ];
    
    $missingTables = [];
    foreach ($requiredTables as $table) {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->rowCount() > 0) {
            // Contar registros
            $countStmt = $conn->query("SELECT COUNT(*) as count FROM $table");
            $count = $countStmt->fetch();
            echo "✅ $table: {$count['count']} registros<br>";
        } else {
            echo "❌ $table: NO EXISTE<br>";
            $missingTables[] = $table;
        }
    }
    
    // 2. Si falta la tabla stock_movements, crearla
    if (in_array('stock_movements', $missingTables)) {
        echo "<h2>2. Creando tabla stock_movements faltante</h2>";
        $createStockMovements = "
            CREATE TABLE stock_movements (
                id INT AUTO_INCREMENT PRIMARY KEY,
                product_id INT NOT NULL,
                type ENUM('entrada', 'salida') NOT NULL,
                quantity INT NOT NULL,
                description TEXT,
                reference_number VARCHAR(100),
                movement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT
            ) ENGINE=InnoDB
        ";
        
        $conn->exec($createStockMovements);
        echo "✅ Tabla stock_movements creada<br>";
    }
    
    // 3. Probar la consulta problemática paso a paso
    echo "<h2>3. Probando consulta SQL paso a paso</h2>";
    
    // Consulta básica sin stock
    echo "<h3>Paso 1: Consulta básica de productos</h3>";
    $basicSql = "SELECT p.id, p.name, p.sku, p.price FROM products p LIMIT 5";
    $stmt = $conn->query($basicSql);
    $basicProducts = $stmt->fetchAll();
    echo "✅ Productos básicos: " . count($basicProducts) . "<br>";
    
    // Consulta con JOINs pero sin stock
    echo "<h3>Paso 2: Consulta con JOINs (sin stock)</h3>";
    $joinSql = "SELECT 
                p.id, p.name, p.sku, p.price,
                ps.name AS status_name,
                pt.name AS product_type_name,
                c.name AS category_name,
                s.name AS subcategory_name
            FROM products p
            LEFT JOIN product_status ps ON p.status_id = ps.id
            LEFT JOIN product_types pt ON p.product_type_id = pt.id
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN subcategories s ON p.subcategory_id = s.id
            LIMIT 5";
    
    try {
        $stmt = $conn->query($joinSql);
        $joinProducts = $stmt->fetchAll();
        echo "✅ Productos con JOINs: " . count($joinProducts) . "<br>";
        
        if (count($joinProducts) > 0) {
            echo "<h4>Primer producto:</h4>";
            echo "<pre>";
            print_r($joinProducts[0]);
            echo "</pre>";
        }
    } catch (Exception $e) {
        echo "❌ Error en consulta con JOINs: " . $e->getMessage() . "<br>";
    }
    
    // Consulta completa con stock
    echo "<h3>Paso 3: Consulta completa con stock</h3>";
    $fullSql = "SELECT 
                p.id, p.name, p.sku, p.price,
                ps.name AS status_name,
                pt.name AS product_type_name,
                c.name AS category_name,
                s.name AS subcategory_name,
                COALESCE(
                    (SELECT SUM(
                        CASE 
                            WHEN type = 'entrada' THEN quantity 
                            WHEN type = 'salida' THEN -quantity 
                        END
                    ) FROM stock_movements 
                    WHERE product_id = p.id), 
                    0
                ) as current_stock
            FROM products p
            LEFT JOIN product_status ps ON p.status_id = ps.id
            LEFT JOIN product_types pt ON p.product_type_id = pt.id
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN subcategories s ON p.subcategory_id = s.id
            LIMIT 5";
    
    try {
        $stmt = $conn->query($fullSql);
        $fullProducts = $stmt->fetchAll();
        echo "✅ Productos completos: " . count($fullProducts) . "<br>";
        
        if (count($fullProducts) > 0) {
            echo "<h4>Primer producto completo:</h4>";
            echo "<pre>";
            print_r($fullProducts[0]);
            echo "</pre>";
        }
    } catch (Exception $e) {
        echo "❌ Error en consulta completa: " . $e->getMessage() . "<br>";
        echo "<p>Vamos a crear un método alternativo sin stock...</p>";
    }
    
    // 4. Crear método alternativo en el modelo
    echo "<h2>4. Creando método alternativo</h2>";
    
    // Probar el modelo directamente
    $productModel = new \App\Models\Product();
    
    // Intentar getAllWithDetails original
    echo "<h3>Método getAllWithDetails original:</h3>";
    try {
        $originalProducts = $productModel->getAllWithDetails();
        echo "✅ Productos obtenidos: " . count($originalProducts) . "<br>";
        
        if (count($originalProducts) > 0) {
            echo "<h4>Primer producto del método original:</h4>";
            echo "<pre>";
            print_r($originalProducts[0]);
            echo "</pre>";
        }
    } catch (Exception $e) {
        echo "❌ Error en método original: " . $e->getMessage() . "<br>";
    }
    
    // Probar método getAll básico
    echo "<h3>Método getAll básico:</h3>";
    try {
        $basicProducts = $productModel->getAll();
        echo "✅ Productos básicos obtenidos: " . count($basicProducts) . "<br>";
        
        if (count($basicProducts) > 0) {
            echo "<h4>Primer producto básico:</h4>";
            echo "<pre>";
            print_r($basicProducts[0]);
            echo "</pre>";
        }
    } catch (Exception $e) {
        echo "❌ Error en método básico: " . $e->getMessage() . "<br>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error crítico:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<h3>🔗 Próximos pasos:</h3>";
echo "<ol>";
echo "<li><a href='/marykaystock/test_products_direct.php'>Probar productos directamente</a></li>";
echo "<li><a href='/marykaystock/products'>Ir a página de productos</a></li>";
echo "<li><a href='/marykaystock/init_database.php'>Re-ejecutar inicialización</a></li>";
echo "</ol>";
?>
