<?php
/**
 * Debug detallado específico para el problema de productos
 */

require_once __DIR__ . '/vendor/autoload.php';

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug Detallado - Problema de Productos</h1>";

try {
    // 1. Verificar conexión y datos básicos
    echo "<h2>1. Conexión y datos básicos</h2>";
    $db = \App\Core\Database::getInstance();
    $conn = $db->getConnection();
    
    $stmt = $conn->query("SELECT COUNT(*) as total FROM products");
    $result = $stmt->fetch();
    echo "📊 Productos en DB: " . $result['total'] . "<br>";
    
    if ($result['total'] == 0) {
        echo "<p style='color: red;'>❌ No hay productos en la base de datos. Ejecuta init_database.php primero.</p>";
        echo "<p><a href='/marykaystock/init_database.php' style='background: #007cba; color: white; padding: 10px; text-decoration: none;'>Ejecutar init_database.php</a></p>";
        exit;
    }
    
    // 2. Probar consulta directa del modelo
    echo "<h2>2. Probando consulta directa del modelo</h2>";
    $productModel = new \App\Models\Product();
    
    // Probar getAll()
    echo "<h3>Método getAll():</h3>";
    $allProducts = $productModel->getAll();
    echo "Productos obtenidos: " . count($allProducts) . "<br>";
    
    if (count($allProducts) > 0) {
        echo "<h4>Primer producto (getAll):</h4>";
        echo "<pre>";
        print_r($allProducts[0]);
        echo "</pre>";
    }
    
    // Probar getAllWithDetails()
    echo "<h3>Método getAllWithDetails():</h3>";
    $detailedProducts = $productModel->getAllWithDetails();
    echo "Productos con detalles: " . count($detailedProducts) . "<br>";
    
    if (count($detailedProducts) > 0) {
        echo "<h4>Primer producto (getAllWithDetails):</h4>";
        echo "<pre>";
        print_r($detailedProducts[0]);
        echo "</pre>";
    }
    
    // 3. Simular exactamente lo que hace el controlador
    echo "<h2>3. Simulando ProductController::index()</h2>";
    
    // Iniciar sesión como lo hace el controlador
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    try {
        // Crear subcategorías predeterminadas
        $subcategoryModel = new \App\Models\Subcategory();
        $subcategoryModel->createDefaultSubcategoriesForAllCategories();
        echo "✅ Subcategorías predeterminadas creadas<br>";
        
        // Obtener productos como lo hace el controlador
        $productModel = new \App\Models\Product();
        $products = $productModel->getAllWithDetails();
        echo "✅ Productos obtenidos: " . count($products) . "<br>";
        
        // Obtener categorías y subcategorías
        $categoryModel = new \App\Models\Category();
        $categories = $categoryModel->getAll();
        echo "✅ Categorías obtenidas: " . count($categories) . "<br>";
        
        $subcategories = $subcategoryModel->getAll();
        echo "✅ Subcategorías obtenidas: " . count($subcategories) . "<br>";
        
        // Mostrar datos que se pasarían a la vista
        echo "<h4>Datos para la vista:</h4>";
        echo "<ul>";
        echo "<li>products: " . count($products) . " elementos</li>";
        echo "<li>categories: " . count($categories) . " elementos</li>";
        echo "<li>subcategories: " . count($subcategories) . " elementos</li>";
        echo "</ul>";
        
        if (count($products) > 0) {
            echo "<h4>Estructura del primer producto:</h4>";
            echo "<pre>";
            print_r($products[0]);
            echo "</pre>";
        }
        
    } catch (Exception $e) {
        echo "❌ Error en simulación del controlador: " . $e->getMessage() . "<br>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    // 4. Verificar la vista directamente
    echo "<h2>4. Probando la vista directamente</h2>";
    
    if (count($products) > 0 && count($categories) > 0) {
        echo "<h3>Simulando la lógica de organización de la vista:</h3>";
        
        // Simular la lógica de organización de productos de la vista
        $organizedProducts = [];
        foreach ($products as $product) {
            $categoryId = $product['category_id'] ?? 0;
            $subcategoryId = $product['subcategory_id'] ?? 0;
            
            if (!isset($organizedProducts[$categoryId])) {
                $organizedProducts[$categoryId] = [
                    'name' => $product['category_name'] ?? '',
                    'description' => $product['category_description'] ?? '',
                    'subcategories' => []
                ];
            }
            
            if (!isset($organizedProducts[$categoryId]['subcategories'][$subcategoryId])) {
                $organizedProducts[$categoryId]['subcategories'][$subcategoryId] = [
                    'name' => $product['subcategory_name'] ?? '',
                    'description' => $product['subcategory_description'] ?? '',
                    'products' => []
                ];
            }
            
            $organizedProducts[$categoryId]['subcategories'][$subcategoryId]['products'][] = $product;
        }
        
        echo "✅ Productos organizados por categorías: " . count($organizedProducts) . "<br>";
        
        foreach ($organizedProducts as $catId => $category) {
            echo "<h4>Categoría: {$category['name']} (ID: $catId)</h4>";
            foreach ($category['subcategories'] as $subId => $subcategory) {
                echo "<h5>&nbsp;&nbsp;Subcategoría: {$subcategory['name']} (ID: $subId) - " . count($subcategory['products']) . " productos</h5>";
                foreach ($subcategory['products'] as $product) {
                    echo "&nbsp;&nbsp;&nbsp;&nbsp;• {$product['name']} (SKU: {$product['sku']})<br>";
                }
            }
        }
    }
    
    // 5. Verificar si hay errores en sesión
    echo "<h2>5. Verificando errores en sesión</h2>";
    if (isset($_SESSION['error'])) {
        echo "<p style='color: red;'>Error en sesión: " . $_SESSION['error'] . "</p>";
    } else {
        echo "✅ No hay errores en sesión<br>";
    }
    
    if (isset($_SESSION['success'])) {
        echo "<p style='color: green;'>Mensaje de éxito: " . $_SESSION['success'] . "</p>";
    }
    
    // 6. Probar el routing
    echo "<h2>6. Información de routing</h2>";
    echo "<ul>";
    echo "<li>REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'N/A') . "</li>";
    echo "<li>BASE_URL definida: " . (defined('BASE_URL') ? BASE_URL : 'NO DEFINIDA') . "</li>";
    echo "<li>Sesión iniciada: " . (session_status() == PHP_SESSION_ACTIVE ? 'SÍ' : 'NO') . "</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error crítico:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<h3>Acciones recomendadas:</h3>";
echo "<ol>";
echo "<li><a href='/marykaystock/init_database.php'>Ejecutar init_database.php</a> (si no hay productos)</li>";
echo "<li><a href='/marykaystock/products'>Ir a página de productos</a></li>";
echo "<li><a href='/marykaystock/'>Ir al inicio</a></li>";
echo "</ol>";
?>
