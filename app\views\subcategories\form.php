<div class="mb-4">
    <label class="block text-gray-700 text-sm font-bold mb-2" for="name">
        Nombre de la Subcategoría
    </label>
    <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
           id="name" type="text" name="name" value="<?= isset($subcategory) ? htmlspecialchars($subcategory['name']) : '' ?>" required>
</div>

<div class="mb-4">
    <label class="block text-gray-700 text-sm font-bold mb-2" for="grouping_type_id">
        Tipo de Agrupación
    </label>
    <select class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="grouping_type_id" name="grouping_type_id" required>
        <option value="">Seleccione un tipo</option>
        <?php foreach ($groupingTypes as $type): ?>
            <option value="<?= $type['id'] ?>" 
                    <?= isset($subcategory) && $subcategory['grouping_type_id'] == $type['id'] ? 'selected' : '' ?>>
                <?= htmlspecialchars($type['name']) ?>
            </option>
        <?php endforeach; ?>
    </select>
</div>