<?php
namespace App\Models;

use App\Core\Model;
use PDO;

class Product extends Model
{
    protected $table = 'products';

    public function getAll()
    {
        $sql = "SELECT p.*,
                c.name as category_name,
                c.description as category_description,
                sc.name as subcategory_name,
                sc.description as subcategory_description,
                COALESCE(
                    (SELECT SUM(
                        CASE 
                            WHEN type = 'entrada' THEN quantity 
                            WHEN type = 'salida' THEN -quantity 
                        END
                    ) FROM stock_movements 
                    WHERE product_id = p.id), 
                    0
                ) as current_stock
                FROM {$this->table} p
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN subcategories sc ON p.subcategory_id = sc.id
                ORDER BY c.name, sc.name, p.sku";

        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (\PDOException $e) {
            error_log("Error in Product getAll: " . $e->getMessage());
            return [];
        }
    }

    public function getAllOrganized()
    {
        $sql = "SELECT 
                p.*,
                c.id as category_id,
                c.name as category_name,
                c.description as category_description,
                sc.id as subcategory_id,
                sc.name as subcategory_name,
                sc.description as subcategory_description
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN subcategories sc ON p.subcategory_id = sc.id
            ORDER BY 
                COALESCE(c.name, 'ZZZ'),
                COALESCE(sc.name, 'ZZZ'),
                p.name";

        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (\PDOException $e) {
            error_log("Error in Product getAllOrganized: " . $e->getMessage());
            throw new \Exception('Error al obtener los productos');
        }
    }

    public function getById($id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindValue(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function create($data)
    {
        try {
            $this->db->beginTransaction();

            // Verificar SKU duplicado
            $sku = trim($data['sku']);
            $checkSql = "SELECT COUNT(*) FROM {$this->table} WHERE BINARY sku = :sku";
            $checkStmt = $this->db->prepare($checkSql);
            $checkStmt->bindValue(':sku', $sku, \PDO::PARAM_STR);
            $checkStmt->execute();
            
            if ($checkStmt->fetchColumn() > 0) {
                throw new \Exception("El SKU ya existe en la base de datos");
            }

            // Preparar la consulta SQL
            $sql = "INSERT INTO {$this->table} (
                        sku, name, description, category_id, subcategory_id,
                        product_type_id, status_id, price, minimum_stock
                    ) VALUES (
                        :sku, :name, :description, :category_id, :subcategory_id,
                        :product_type_id, :status_id, :price, :minimum_stock
                    )";

            $stmt = $this->db->prepare($sql);
            
            // Bind de los parámetros
            $params = [
                ':sku' => $sku,
                ':name' => $data['name'],
                ':description' => $data['description'],
                ':category_id' => $data['category_id'],
                ':subcategory_id' => $data['subcategory_id'],
                ':product_type_id' => $data['product_type_id'],
                ':status_id' => $data['status_id'],
                ':price' => $data['price'],
                ':minimum_stock' => $data['minimum_stock']
            ];

            $stmt->execute($params);
            $newId = $this->db->lastInsertId();
            
            $this->db->commit();
            return $newId;

        } catch (\PDOException $e) {
            $this->db->rollBack();
            error_log("Error en Product::create: " . $e->getMessage());
            throw new \Exception("Error al crear el producto: " . $e->getMessage());
        }
    }

    public function update($id, $data)
    {
        try {
            // Obtener precio actual antes de la actualización
            $currentProduct = $this->getById($id);
            $oldPrice = $currentProduct['price'];
            
            // Realizar la actualización
            $result = parent::update($id, $data);
            
            // Si el precio cambió, registrar en el historial
            if ($result && isset($data['price']) && $data['price'] != $oldPrice) {
                $priceHistory = new PriceHistory();
                $priceHistory->create([
                    'product_id' => $id,
                    'old_price' => $oldPrice,
                    'new_price' => $data['price'],
                    'change_type' => 'individual',
                    'changed_by' => $_SESSION['user_id']
                ]);
            }
            
            return $result;
        } catch (\PDOException $e) {
            error_log("Database error in Product update: " . $e->getMessage());
            throw $e;
        }
    }

    public function delete($id)
    {
        $sql = "DELETE FROM products WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute(['id' => $id]);
    }

    public function getBySku($sku)
    {
        try {
            $sku = trim($sku);
            error_log("=== BUSCANDO SKU EN BASE DE DATOS ===");
            error_log("SKU a buscar: '" . $sku . "'");

            // Primero, veamos todos los SKUs en la base de datos
            $debugSql = "SELECT id, sku FROM {$this->table}";
            $debugStmt = $this->db->query($debugSql);
            error_log("SKUs existentes en la base de datos:");
            while ($row = $debugStmt->fetch(\PDO::FETCH_ASSOC)) {
                error_log("ID: {$row['id']}, SKU: '{$row['sku']}'");
            }

            // Ahora busquemos el SKU específico
            $sql = "SELECT * FROM {$this->table} WHERE BINARY sku = :sku";
            $stmt = $this->db->prepare($sql);
            $stmt->bindValue(':sku', $sku, \PDO::PARAM_STR);
            $stmt->execute();
            
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            error_log("Resultado de búsqueda:");
            error_log($result ? "SKU encontrado: " . print_r($result, true) : "SKU no encontrado");
            
            return $result;
        } catch (\PDOException $e) {
            error_log("Error en getBySku: " . $e->getMessage());
            throw $e;
        }
    }

    public function getCurrentStock($productId)
    {
        $sql = "SELECT COALESCE(SUM(
                    CASE 
                        WHEN type = 'entrada' THEN quantity 
                        WHEN type = 'salida' THEN -quantity 
                    END
                ), 0) as stock 
                FROM stock_movements 
                WHERE product_id = :product_id";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':product_id' => $productId]);
        
        return $stmt->fetchColumn();
    }

    public function getProductsWithLowStock()
    {
        $sql = "SELECT p.*, 
                COALESCE(SUM(
                    CASE 
                        WHEN sm.type = 'entrada' THEN sm.quantity 
                        WHEN sm.type = 'salida' THEN -sm.quantity 
                    END
                ), 0) as current_stock 
                FROM {$this->table} p 
                LEFT JOIN stock_movements sm ON p.id = sm.product_id 
                GROUP BY p.id 
                HAVING current_stock <= p.minimum_stock";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getTotalProducts()
    {
        $sql = "SELECT COUNT(*) FROM {$this->table}";
        return $this->db->query($sql)->fetchColumn();
    }

    public function getNoStockCount()
    {
        $sql = "SELECT COUNT(DISTINCT p.id) as count
                FROM {$this->table} p
                LEFT JOIN (
                    SELECT product_id, 
                           SUM(CASE 
                               WHEN type = 'entrada' THEN quantity 
                               WHEN type = 'salida' THEN -quantity 
                           END) as current_stock
                    FROM stock_movements
                    GROUP BY product_id
                ) sm ON p.id = sm.product_id
                WHERE COALESCE(sm.current_stock, 0) = 0";
    
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchColumn();
        } catch (\PDOException $e) {
            error_log("Error in getNoStockCount: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Actualiza el estado de múltiples productos
     * 
     * @param array $productIds IDs de los productos a actualizar
     * @param int $statusId ID del nuevo estado
     * @return bool Resultado de la operación
     */
    public function updateStatusBulk($productIds, $statusId)
    {
        try {
            if (empty($productIds) || !$statusId) {
                return false;
            }
            
            // Registrar para depuración
            error_log("updateStatusBulk - productIds: " . implode(', ', $productIds));
            error_log("updateStatusBulk - statusId: " . $statusId);
            
            $placeholders = implode(',', array_fill(0, count($productIds), '?'));
            $sql = "UPDATE products SET status_id = ? WHERE id IN ($placeholders)";
            
            $stmt = $this->db->prepare($sql);
            
            // Añadir statusId como primer parámetro
            $params = [$statusId];
            
            // Añadir los IDs de productos
            foreach ($productIds as $id) {
                $params[] = $id;
            }
            
            $stmt->execute($params);
            return true;
        } catch (\Exception $e) {
            error_log("Error en Product::updateStatusBulk: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Actualiza el tipo de múltiples productos
     * 
     * @param array $productIds IDs de los productos a actualizar
     * @param int $typeId ID del nuevo tipo
     * @return bool Resultado de la operación
     */
    public function updateTypeBulk($productIds, $typeId)
    {
        try {
            if (empty($productIds) || !$typeId) {
                return false;
            }
            
            // Registrar para depuración
            error_log("updateTypeBulk - productIds: " . implode(', ', $productIds));
            error_log("updateTypeBulk - typeId: " . $typeId);
            
            $placeholders = implode(',', array_fill(0, count($productIds), '?'));
            $sql = "UPDATE products SET product_type_id = ? WHERE id IN ($placeholders)";
            
            $stmt = $this->db->prepare($sql);
            
            // Añadir typeId como primer parámetro
            $params = [$typeId];
            
            // Añadir los IDs de productos
            foreach ($productIds as $id) {
                $params[] = $id;
            }
            
            $stmt->execute($params);
            return true;
        } catch (\Exception $e) {
            error_log("Error en Product::updateTypeBulk: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Obtiene todos los productos con detalles adicionales
     * 
     * @return array Arreglo con todos los productos y sus detalles
     */
    public function getAllWithDetails()
    {
        // Primero intentar la consulta completa con stock
        $sql = "SELECT
                p.id,
                p.name,
                p.sku,
                p.price,
                p.status_id,
                p.product_type_id,
                p.category_id,
                p.subcategory_id,
                ps.name AS status_name,
                pt.name AS product_type_name,
                c.name AS category_name,
                c.description AS category_description,
                s.name AS subcategory_name,
                s.description AS subcategory_description,
                COALESCE(
                    (SELECT SUM(
                        CASE
                            WHEN type = 'entrada' THEN quantity
                            WHEN type = 'salida' THEN -quantity
                        END
                    ) FROM stock_movements
                    WHERE product_id = p.id),
                    0
                ) as current_stock
            FROM {$this->table} p
            LEFT JOIN product_status ps ON p.status_id = ps.id
            LEFT JOIN product_types pt ON p.product_type_id = pt.id
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN subcategories s ON p.subcategory_id = s.id
            ORDER BY c.name, s.name, p.name";

        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (\PDOException $e) {
            error_log("Error in Product getAllWithDetails (with stock): " . $e->getMessage());

            // Si falla, intentar sin el cálculo de stock
            return $this->getAllWithDetailsNoStock();
        }
    }

    /**
     * Versión alternativa sin cálculo de stock
     */
    public function getAllWithDetailsNoStock()
    {
        $sql = "SELECT
                p.id,
                p.name,
                p.sku,
                p.price,
                p.status_id,
                p.product_type_id,
                p.category_id,
                p.subcategory_id,
                ps.name AS status_name,
                pt.name AS product_type_name,
                c.name AS category_name,
                c.description AS category_description,
                s.name AS subcategory_name,
                s.description AS subcategory_description,
                0 as current_stock
            FROM {$this->table} p
            LEFT JOIN product_status ps ON p.status_id = ps.id
            LEFT JOIN product_types pt ON p.product_type_id = pt.id
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN subcategories s ON p.subcategory_id = s.id
            ORDER BY c.name, s.name, p.name";

        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (\PDOException $e) {
            error_log("Error in Product getAllWithDetailsNoStock: " . $e->getMessage());
            $_SESSION['error'] = "Error al cargar los productos: " . $e->getMessage();
            return [];
        }
    }
}







