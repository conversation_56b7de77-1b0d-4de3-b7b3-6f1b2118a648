<?php
/**
 * Script de debug para verificar la conexión a la base de datos y los productos
 */

// Incluir el autoloader
require_once __DIR__ . '/vendor/autoload.php';

// Configuración de errores
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug de Productos - <PERSON></h1>";

try {
    // 1. Verificar conexión a la base de datos
    echo "<h2>1. Verificando conexión a la base de datos</h2>";
    $db = \App\Core\Database::getInstance();
    $conn = $db->getConnection();
    echo "✅ Conexión exitosa a la base de datos<br>";
    
    // 2. Verificar que las tablas existen
    echo "<h2>2. Verificando tablas</h2>";
    $tables = ['products', 'categories', 'subcategories', 'product_types', 'product_status'];
    
    foreach ($tables as $table) {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->rowCount() > 0) {
            echo "✅ Tabla '$table' existe<br>";
        } else {
            echo "❌ Tabla '$table' NO existe<br>";
        }
    }
    
    // 3. Contar registros en cada tabla
    echo "<h2>3. Contando registros</h2>";
    foreach ($tables as $table) {
        try {
            $stmt = $conn->query("SELECT COUNT(*) as count FROM $table");
            $result = $stmt->fetch();
            echo "📊 Tabla '$table': {$result['count']} registros<br>";
        } catch (Exception $e) {
            echo "❌ Error al contar '$table': " . $e->getMessage() . "<br>";
        }
    }
    
    // 4. Mostrar algunos productos si existen
    echo "<h2>4. Productos en la base de datos</h2>";
    $stmt = $conn->query("SELECT * FROM products LIMIT 10");
    $products = $stmt->fetchAll();
    
    if (empty($products)) {
        echo "⚠️ No hay productos en la base de datos<br>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>SKU</th><th>Nombre</th><th>Precio</th><th>Categoría ID</th><th>Estado ID</th></tr>";
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td>{$product['id']}</td>";
            echo "<td>{$product['sku']}</td>";
            echo "<td>{$product['name']}</td>";
            echo "<td>\${$product['price']}</td>";
            echo "<td>{$product['category_id']}</td>";
            echo "<td>{$product['status_id']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 5. Verificar categorías
    echo "<h2>5. Categorías disponibles</h2>";
    $stmt = $conn->query("SELECT * FROM categories");
    $categories = $stmt->fetchAll();
    
    if (empty($categories)) {
        echo "⚠️ No hay categorías en la base de datos<br>";
    } else {
        echo "<ul>";
        foreach ($categories as $category) {
            echo "<li>ID: {$category['id']} - {$category['name']}</li>";
        }
        echo "</ul>";
    }
    
    // 6. Verificar estados de productos
    echo "<h2>6. Estados de productos disponibles</h2>";
    $stmt = $conn->query("SELECT * FROM product_status");
    $statuses = $stmt->fetchAll();
    
    if (empty($statuses)) {
        echo "⚠️ No hay estados de productos en la base de datos<br>";
    } else {
        echo "<ul>";
        foreach ($statuses as $status) {
            echo "<li>ID: {$status['id']} - {$status['name']}</li>";
        }
        echo "</ul>";
    }
    
    // 7. Verificar tipos de productos
    echo "<h2>7. Tipos de productos disponibles</h2>";
    $stmt = $conn->query("SELECT * FROM product_types");
    $types = $stmt->fetchAll();
    
    if (empty($types)) {
        echo "⚠️ No hay tipos de productos en la base de datos<br>";
    } else {
        echo "<ul>";
        foreach ($types as $type) {
            echo "<li>ID: {$type['id']} - {$type['name']}</li>";
        }
        echo "</ul>";
    }
    
    // 8. Probar el modelo Product
    echo "<h2>8. Probando modelo Product</h2>";
    $productModel = new \App\Models\Product();
    $allProducts = $productModel->getAllWithDetails();
    
    echo "Productos obtenidos por el modelo: " . count($allProducts) . "<br>";
    
    if (!empty($allProducts)) {
        echo "<h3>Primeros 3 productos del modelo:</h3>";
        echo "<pre>";
        print_r(array_slice($allProducts, 0, 3));
        echo "</pre>";
    }
    
    // 9. Verificar estructura de la tabla products
    echo "<h2>9. Estructura de la tabla products</h2>";
    $stmt = $conn->query("DESCRIBE products");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p><strong>Trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><strong>Información del servidor:</strong></p>";
echo "<ul>";
echo "<li>PHP Version: " . phpversion() . "</li>";
echo "<li>MySQL disponible: " . (extension_loaded('pdo_mysql') ? 'Sí' : 'No') . "</li>";
echo "<li>Directorio actual: " . __DIR__ . "</li>";
echo "</ul>";
?>
