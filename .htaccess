# Configuración principal para <PERSON> Stock
# Este archivo maneja las redirecciones desde la raíz del proyecto

# Prevenir acceso a archivos sensibles
<Files .htaccess>
    Order allow,deny
    Deny from all
</Files>

# Proteger archivos de configuración y sensibles
<FilesMatch "^(\.env|\.git|composer\.(json|lock)|package\.(json|lock)|\.md)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Proteger directorios sensibles
RedirectMatch 403 ^/?(app|database|vendor)/.*$

# Configurar tipos MIME correctamente
<IfModule mod_mime.c>
    AddType text/css .css
    AddType application/javascript .js
    AddType text/javascript .js
    AddType image/svg+xml .svg
    AddType font/woff .woff
    AddType font/woff2 .woff2
    AddType font/ttf .ttf
    AddType application/vnd.ms-fontobject .eot
</IfModule>

# Configuración de reescritura de URLs
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Establecer la base correcta (ajustar según tu configuración)
    RewriteBase /marykaystock/
    
    # Permitir acceso directo a archivos estáticos existentes
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteRule \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|pdf|txt)$ - [L]
    
    # Permitir acceso directo a directorios existentes
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteRule ^.*$ - [L]
    
    # Si el archivo o directorio no existe físicamente, redirigir a index.php
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ index.php [QSA,L]
</IfModule>

# Comprimir archivos para mejor rendimiento
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/css application/javascript text/javascript application/json text/xml application/xml
</IfModule>

# Control de cache para archivos estáticos
<IfModule mod_expires.c>
    ExpiresActive On
    
    # CSS y JavaScript
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType text/javascript "access plus 1 year"
    
    # Imágenes
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # Fuentes
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType application/vnd.ms-fontobject "access plus 1 year"
    
    # HTML y otros
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType application/json "access plus 1 hour"
</IfModule>

# Headers de seguridad
<IfModule mod_headers.c>
    # Prevenir sniffing de tipos MIME
    Header set X-Content-Type-Options "nosniff"
    
    # Protección XSS
    Header set X-XSS-Protection "1; mode=block"
    
    # Prevenir clickjacking
    Header set X-Frame-Options "SAMEORIGIN"
    
    # Política de referrer
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Para archivos estáticos, agregar cache control
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
        Header set Cache-Control "public, max-age=31536000"
    </FilesMatch>
</IfModule>

# Deshabilitar listado de directorios
Options -Indexes

# Configuración de PHP (si es necesario)
<IfModule mod_php.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_vars 3000
</IfModule>

# Protección adicional contra ataques
<IfModule mod_security.c>
    SecFilterEngine Off
    SecFilterScanPOST Off
</IfModule>

# Redirección de errores personalizados (opcional)
ErrorDocument 404 /marykaystock/index.php
ErrorDocument 403 /marykaystock/index.php
ErrorDocument 500 /marykaystock/index.php
