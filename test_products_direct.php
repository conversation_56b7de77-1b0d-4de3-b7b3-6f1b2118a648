<?php
/**
 * Test directo de productos sin usar el router
 */

require_once __DIR__ . '/vendor/autoload.php';

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Configurar como lo hace index.php
$requestUri = $_SERVER['REQUEST_URI'];
$scriptName = $_SERVER['SCRIPT_NAME'];
$basePath = dirname($scriptName);
if ($basePath === '/') {
    $basePath = '';
}
define('BASE_URL', $basePath);

session_start();

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Directo - Productos</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .product-card { border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .category { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .subcategory { background: #f8f8f8; padding: 8px; margin: 5px 0; border-left: 3px solid #007cba; }
        .error { color: red; background: #ffe6e6; padding: 10px; border-radius: 5px; }
        .success { color: green; background: #e6ffe6; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🧪 Test Directo de Productos</h1>
    
    <?php
    try {
        echo "<div class='success'>✅ Configuración inicial completada</div>";
        
        // Verificar base de datos
        $db = \App\Core\Database::getInstance();
        $conn = $db->getConnection();
        
        $stmt = $conn->query("SELECT COUNT(*) as total FROM products");
        $result = $stmt->fetch();
        
        if ($result['total'] == 0) {
            echo "<div class='error'>❌ No hay productos en la base de datos. <a href='/marykaystock/init_database.php'>Ejecutar init_database.php</a></div>";
            exit;
        }
        
        echo "<div class='success'>📊 Productos encontrados: " . $result['total'] . "</div>";
        
        // Obtener productos directamente
        $productModel = new \App\Models\Product();
        $products = $productModel->getAllWithDetails();
        
        echo "<div class='success'>✅ Productos obtenidos del modelo: " . count($products) . "</div>";
        
        if (empty($products)) {
            echo "<div class='error'>❌ El modelo no devolvió productos</div>";
        } else {
            // Organizar productos por categoría
            $organizedProducts = [];
            foreach ($products as $product) {
                $categoryId = $product['category_id'] ?? 0;
                $subcategoryId = $product['subcategory_id'] ?? 0;
                
                if (!isset($organizedProducts[$categoryId])) {
                    $organizedProducts[$categoryId] = [
                        'name' => $product['category_name'] ?? 'Sin Categoría',
                        'subcategories' => []
                    ];
                }
                
                if (!isset($organizedProducts[$categoryId]['subcategories'][$subcategoryId])) {
                    $organizedProducts[$categoryId]['subcategories'][$subcategoryId] = [
                        'name' => $product['subcategory_name'] ?? 'Sin Subcategoría',
                        'products' => []
                    ];
                }
                
                $organizedProducts[$categoryId]['subcategories'][$subcategoryId]['products'][] = $product;
            }
            
            echo "<h2>📦 Productos Organizados</h2>";
            
            foreach ($organizedProducts as $categoryId => $category) {
                echo "<div class='category'>";
                echo "<h3>📁 {$category['name']} (ID: $categoryId)</h3>";
                
                foreach ($category['subcategories'] as $subcategoryId => $subcategory) {
                    echo "<div class='subcategory'>";
                    echo "<h4>📂 {$subcategory['name']} (ID: $subcategoryId)</h4>";
                    
                    foreach ($subcategory['products'] as $product) {
                        echo "<div class='product-card'>";
                        echo "<strong>📦 {$product['name']}</strong><br>";
                        echo "SKU: {$product['sku']}<br>";
                        echo "Precio: $" . number_format($product['price'], 2) . "<br>";
                        echo "Stock: " . ($product['current_stock'] ?? 0) . "<br>";
                        echo "Estado: " . ($product['status_name'] ?? 'N/A') . "<br>";
                        echo "Tipo: " . ($product['product_type_name'] ?? 'N/A') . "<br>";
                        echo "</div>";
                    }
                    
                    echo "</div>";
                }
                
                echo "</div>";
            }
        }
        
        // Información adicional
        echo "<h2>🔍 Información de Debug</h2>";
        echo "<ul>";
        echo "<li>BASE_URL: " . BASE_URL . "</li>";
        echo "<li>REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "</li>";
        echo "<li>SCRIPT_NAME: " . $_SERVER['SCRIPT_NAME'] . "</li>";
        echo "<li>Sesión activa: " . (session_status() == PHP_SESSION_ACTIVE ? 'SÍ' : 'NO') . "</li>";
        echo "</ul>";
        
        if (isset($_SESSION['error'])) {
            echo "<div class='error'>Error en sesión: " . $_SESSION['error'] . "</div>";
            unset($_SESSION['error']);
        }
        
        if (isset($_SESSION['success'])) {
            echo "<div class='success'>Mensaje de éxito: " . $_SESSION['success'] . "</div>";
            unset($_SESSION['success']);
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<h3>❌ Error:</h3>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
        echo "</div>";
    }
    ?>
    
    <hr>
    <h3>🔗 Enlaces</h3>
    <ul>
        <li><a href="/marykaystock/products">🔗 Página de productos oficial</a></li>
        <li><a href="/marykaystock/">🏠 Inicio</a></li>
        <li><a href="/marykaystock/init_database.php">🔧 Inicializar base de datos</a></li>
    </ul>
</body>
</html>
