<?php
/**
 * Script para inicializar la base de datos con datos básicos
 */

require_once __DIR__ . '/vendor/autoload.php';

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Inicialización de Base de Datos - <PERSON></h1>";

try {
    $db = \App\Core\Database::getInstance();
    $conn = $db->getConnection();
    
    echo "<h2>1. Verificando conexión...</h2>";
    echo "✅ Conexión exitosa<br><br>";
    
    // 1. Insertar tipos de agrupación si no existen
    echo "<h2>2. Insertando tipos de agrupación...</h2>";
    $conn->exec("INSERT IGNORE INTO grouping_types (name) VALUES ('Agrupable'), ('No Agrupable')");
    echo "✅ Tipos de agrupación insertados<br><br>";
    
    // 2. Insertar categorías básicas
    echo "<h2>3. Insertando categorías básicas...</h2>";
    $categories = [
        ['Maquillaje', 'Productos de maquillaje y belleza', 1],
        ['Fragancias', 'Perfumes y colonias', 1],
        ['Cuidado Personal', 'Productos de cuidado e higiene personal', 1],
        ['Accesorios', 'Accesorios y complementos', 2]
    ];
    
    $stmt = $conn->prepare("INSERT IGNORE INTO categories (name, description, grouping_type_id) VALUES (?, ?, ?)");
    foreach ($categories as $category) {
        $stmt->execute($category);
        echo "✅ Categoría '{$category[0]}' insertada<br>";
    }
    echo "<br>";
    
    // 3. Insertar subcategorías básicas
    echo "<h2>4. Insertando subcategorías básicas...</h2>";
    $subcategories = [
        [1, 'Labiales', 'Labiales y productos para labios', 1],
        [1, 'Base y Corrector', 'Bases de maquillaje y correctores', 1],
        [1, 'Ojos', 'Productos para maquillaje de ojos', 1],
        [2, 'Mujer', 'Fragancias para mujer', 1],
        [2, 'Hombre', 'Fragancias para hombre', 1],
        [3, 'Cabello', 'Productos para el cuidado del cabello', 1],
        [3, 'Piel', 'Productos para el cuidado de la piel', 1],
        [4, 'Bolsos', 'Bolsos y carteras', 2],
        [4, 'Joyería', 'Joyería y bisutería', 2]
    ];
    
    $stmt = $conn->prepare("INSERT IGNORE INTO subcategories (category_id, name, description, grouping_type_id) VALUES (?, ?, ?, ?)");
    foreach ($subcategories as $subcategory) {
        $stmt->execute($subcategory);
        echo "✅ Subcategoría '{$subcategory[1]}' insertada<br>";
    }
    echo "<br>";
    
    // 4. Insertar tipos de producto
    echo "<h2>5. Insertando tipos de producto...</h2>";
    $productTypes = [
        ['Regular', 'Productos de línea regular'],
        ['Temporada', 'Productos de temporada'],
        ['Promocional', 'Productos promocionales'],
        ['Premium', 'Productos de gama alta'],
        ['Descontinuado', 'Productos descontinuados']
    ];
    
    $stmt = $conn->prepare("INSERT IGNORE INTO product_types (name, description) VALUES (?, ?)");
    foreach ($productTypes as $type) {
        $stmt->execute($type);
        echo "✅ Tipo '{$type[0]}' insertado<br>";
    }
    echo "<br>";
    
    // 5. Insertar estados de producto
    echo "<h2>6. Insertando estados de producto...</h2>";
    $productStatus = [
        ['Activo', 'Producto disponible para venta'],
        ['Inactivo', 'Producto no disponible temporalmente'],
        ['Descontinuado', 'Producto descontinuado'],
        ['Agotado', 'Producto sin stock']
    ];
    
    $stmt = $conn->prepare("INSERT IGNORE INTO product_status (name, description) VALUES (?, ?)");
    foreach ($productStatus as $status) {
        $stmt->execute($status);
        echo "✅ Estado '{$status[0]}' insertado<br>";
    }
    echo "<br>";
    
    // 6. Insertar productos de ejemplo
    echo "<h2>7. Insertando productos de ejemplo...</h2>";
    $products = [
        ['MK001', 'Labial Mate Rojo Clásico', 'Labial de larga duración color rojo clásico', 1, 1, 1, 1, 29.99, 10],
        ['MK002', 'Base Líquida Tono Natural', 'Base de maquillaje líquida tono natural', 1, 2, 1, 1, 45.99, 8],
        ['MK003', 'Máscara de Pestañas Volumen', 'Máscara para pestañas efecto volumen', 1, 3, 1, 1, 24.99, 15],
        ['MK004', 'Perfume Floral Mujer 50ml', 'Fragancia floral para mujer 50ml', 2, 4, 1, 1, 89.99, 5],
        ['MK005', 'Colonia Fresca Hombre 100ml', 'Colonia fresca para hombre 100ml', 2, 5, 1, 1, 79.99, 5],
        ['MK006', 'Shampoo Revitalizante', 'Shampoo para cabello dañado y seco', 3, 6, 1, 1, 19.99, 20],
        ['MK007', 'Crema Hidratante Facial', 'Crema hidratante para rostro', 3, 7, 1, 1, 34.99, 12],
        ['MK008', 'Bolso Elegante Negro', 'Bolso elegante color negro', 4, 8, 1, 1, 149.99, 3],
        ['MK009', 'Collar Perlas Sintéticas', 'Collar de perlas sintéticas', 4, 9, 1, 1, 59.99, 5],
        ['MK010', 'Kit Labiales Temporada', 'Kit especial de labiales de temporada', 1, 1, 2, 1, 79.99, 8]
    ];
    
    $stmt = $conn->prepare("INSERT IGNORE INTO products (sku, name, description, category_id, subcategory_id, product_type_id, status_id, price, minimum_stock) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
    foreach ($products as $product) {
        $stmt->execute($product);
        echo "✅ Producto '{$product[1]}' (SKU: {$product[0]}) insertado<br>";
    }
    echo "<br>";
    
    // 7. Crear usuario admin si no existe
    echo "<h2>8. Creando usuario administrador...</h2>";
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $conn->prepare("INSERT IGNORE INTO users (username, password, name, email, role) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute(['admin', $adminPassword, 'Administrador', '<EMAIL>', 'admin']);
    echo "✅ Usuario admin creado (usuario: admin, contraseña: admin123)<br><br>";
    
    // 8. Verificar resultados
    echo "<h2>9. Verificando resultados...</h2>";
    $tables = [
        'categories' => 'categorías',
        'subcategories' => 'subcategorías', 
        'product_types' => 'tipos de producto',
        'product_status' => 'estados de producto',
        'products' => 'productos',
        'users' => 'usuarios'
    ];
    
    foreach ($tables as $table => $name) {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM $table");
        $result = $stmt->fetch();
        echo "📊 {$name}: {$result['count']} registros<br>";
    }
    
    echo "<br><h2>✅ ¡Inicialización completada exitosamente!</h2>";
    echo "<p><a href='/marykaystock/products' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Ver Productos</a></p>";
    echo "<p><a href='/marykaystock/login' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Iniciar Sesión</a></p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error durante la inicialización:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
