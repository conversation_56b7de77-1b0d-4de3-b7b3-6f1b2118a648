<div class="container mx-auto px-4">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold"><?= $title ?></h1>
        <a href="<?= BASE_URL ?>/stock" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Volver
        </a>
    </div>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <?= $_SESSION['error'] ?>
            <?php unset($_SESSION['error']) ?>
        </div>
    <?php endif; ?>

    <div class="bg-white shadow-md rounded my-6">
        <?php if (empty($products)): ?>
            <div class="p-4 text-center text-gray-500">
                No hay productos con stock bajo.
            </div>
        <?php else: ?>
            <table class="min-w-full table-auto">
                <thead>
                    <tr class="bg-gray-200 text-gray-600 uppercase text-sm leading-normal">
                        <th class="py-3 px-6 text-left">SKU</th>
                        <th class="py-3 px-6 text-left">Nombre</th>
                        <th class="py-3 px-6 text-center">Stock Actual</th>
                        <th class="py-3 px-6 text-center">Stock Mínimo</th>
                        <th class="py-3 px-6 text-center">Estado</th>
                        <th class="py-3 px-6 text-center">Acciones</th>
                    </tr>
                </thead>
                <tbody class="text-gray-600 text-sm font-light">
                    <?php foreach ($products as $product): ?>
                        <tr class="border-b border-gray-200 hover:bg-gray-100">
                            <td class="py-3 px-6 text-left">
                                <?= htmlspecialchars($product['sku']) ?>
                            </td>
                            <td class="py-3 px-6 text-left">
                                <?= htmlspecialchars($product['name']) ?>
                            </td>
                            <td class="py-3 px-6 text-center">
                                <?= htmlspecialchars($product['current_stock']) ?>
                            </td>
                            <td class="py-3 px-6 text-center">
                                <?= htmlspecialchars($product['minimum_stock']) ?>
                            </td>
                            <td class="py-3 px-6 text-center">
                                <span class="bg-red-200 text-red-700 py-1 px-3 rounded-full text-xs">
                                    Stock Bajo
                                </span>
                            </td>
                            <td class="py-3 px-6 text-center">
                                <a href="<?= BASE_URL ?>/stock/entry" 
                                   class="bg-green-500 hover:bg-green-700 text-white py-1 px-3 rounded text-xs">
                                    Agregar Stock
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>
